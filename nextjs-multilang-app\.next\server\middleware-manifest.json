{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_cd76b067._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_75a74226._.js", "server/edge/chunks/[root-of-the-server]__345a0fa9._.js", "server/edge/chunks/edge-wrapper_9191e9f9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh|en|ja))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(zh|en|ja)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CPMI/zEbCknzvIHZWmaMvRokN9Bs87M7zmty+a43oIE=", "__NEXT_PREVIEW_MODE_ID": "103b8c2d7659ee556057506b82bc5412", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7dcfefbe1ba4060358199ffae75e3ed9a4576be29473f17cd4abbc4511336599", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ca512ddecd5a645b6c0f78f7d24b492304095562eb623f31f5f9b1cb03581110"}}}, "sortedMiddleware": ["/"], "functions": {}}