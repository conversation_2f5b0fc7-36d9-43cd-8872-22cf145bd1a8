@import "tailwindcss";

/* Hero Section Background */
.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center top,
    rgba(255, 46, 77, 0.4) 0%,
    rgba(124, 58, 237, 0.25) 30%,
    rgba(6, 182, 212, 0.1) 60%,
    transparent 80%
  );
  pointer-events: none;
  z-index: 0;
}

:root {
  /* PersonaRoll Color Scheme - extracted from landing.css */
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #a1a1a1;
  --text-muted: #666666;
  --accent-red: #ff2e4d;
  --accent-purple: #7c3aed;
  --accent-cyan: #06b6d4;
  --border-color: #222222;
  --border-hover: #333333;
  --gradient-primary: linear-gradient(
    135deg,
    #ff2e4d 0%,
    #7c3aed 50%,
    #06b6d4 100%
  );
  --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 8px 40px rgba(0, 0, 0, 0.4);
  --shadow-accent: 0 10px 30px rgba(255, 46, 77, 0.2);

  /* Legacy variables for compatibility */
  --background: var(--bg-primary);
  --foreground: var(--text-primary);
}

@theme inline {
  /* Background colors */
  --color-bg-primary: var(--bg-primary);
  --color-bg-secondary: var(--bg-secondary);
  --color-bg-tertiary: var(--bg-tertiary);

  /* Text colors */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);

  /* Accent colors */
  --color-accent-red: var(--accent-red);
  --color-accent-purple: var(--accent-purple);
  --color-accent-cyan: var(--accent-cyan);

  /* Border colors */
  --color-border: var(--border-color);
  --color-border-hover: var(--border-hover);

  /* Legacy compatibility */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  /* rgba(255, 46, 77, 0.15)
   */
  --hero-gradient: radial-gradient(
    ellipse at center top,
    rgba(255, 46, 77, 0.1) 0%,
    transparent 50%
  );

  /* Gradient utilities */
  --gradient-primary: var(--gradient-primary);

  /* Shadow utilities */
  --shadow-soft: var(--shadow-soft);
  --shadow-medium: var(--shadow-medium);
  --shadow-accent: var(--shadow-accent);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  background: var(--background);
  color: var(--foreground);
  font-size: 16px;
  overflow-x: hidden;
}

/* Animations */
@keyframes float-1 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(2deg);
  }
}

@keyframes float-2 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(-1deg);
  }
}

@keyframes float-3 {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-25px) rotate(1deg);
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-float-1 {
  animation: float-1 6s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 8s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 7s ease-in-out infinite;
}

.animate-in {
  animation: animate-in 0.6s ease-out forwards;
}

/* Feature cards hover effects */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
}

/* Screenshot items */
.screenshot-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.screenshot-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Hero Section */
.hero {
  padding: 120px 80px;
  position: relative;
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 100vh;
  max-width: 1400px;
  margin: 0 auto;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center top,
    rgba(255, 46, 77, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
  z-index: 0;
}

.hero-content {
  max-width: 600px;
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  padding: 12px 24px;
  border-radius: 50px;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 46, 77, 0.2);
}

.hero-title {
  font-weight: 800;
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 400px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 900;
  margin-bottom: 8px;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-visual {
  position: relative;
  height: 500px;
  z-index: 2;
}

.hero-mockup {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-card {
  position: absolute;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-medium);
}

.floating-card.card-1 {
  top: 20%;
  left: 10%;
}

.floating-card.card-2 {
  top: 10%;
  right: 20%;
}

.floating-card.card-3 {
  bottom: 20%;
  left: 20%;
}

.persona-preview {
  display: flex;
  align-items: center;
  gap: 15px;
}

.persona-preview img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.persona-preview h4 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.persona-preview p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.platform-icons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.source-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.source-icon:hover {
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
}

.content-preview {
  max-width: 250px;
}

.preview-image {
  margin-bottom: 15px;
}

.content-preview p {
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.preview-meta {
  font-size: 0.75rem;
  color: var(--accent-cyan);
  font-weight: 500;
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Buttons */
.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-accent);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 46, 77, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.1);
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 80px 0;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .floating-card {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    margin-bottom: 20px;
  }

  .hero-visual {
    height: auto;
  }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  padding: 16px 0;
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.logo-icon.small {
  width: 32px;
  height: 32px;
}

.persona-layers {
  position: relative;
  width: 100%;
  height: 100%;
}

.persona-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.layer-1 {
  background: linear-gradient(135deg, var(--accent-red), #ff6b7a);
  animation: rotate1 8s linear infinite;
}

.layer-2 {
  background: linear-gradient(135deg, var(--accent-purple), #a855f7);
  transform: scale(0.85) rotate(120deg);
  animation: rotate2 10s linear infinite reverse;
  opacity: 0.8;
}

.layer-3 {
  background: linear-gradient(135deg, var(--accent-cyan), #22d3ee);
  transform: scale(0.7) rotate(240deg);
  animation: rotate3 12s linear infinite;
  opacity: 0.6;
}

@keyframes rotate1 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate2 {
  from {
    transform: scale(0.85) rotate(120deg);
  }
  to {
    transform: scale(0.85) rotate(480deg);
  }
}

@keyframes rotate3 {
  from {
    transform: scale(0.7) rotate(240deg);
  }
  to {
    transform: scale(0.7) rotate(600deg);
  }
}

.logo-text {
  font-size: 20px;
  font-weight: 900;
  color: var(--text-primary);
}

.nav-cta {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Features Section */
.features {
  padding: 100px 0;
  position: relative;
  background: var(--bg-secondary);
}

.features::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  margin-left: -50vw;
  margin-right: -50vw;
  background: var(--bg-secondary);
  z-index: -1;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.1;
}

.section-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
}

.feature-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.feature-card.featured {
  background: linear-gradient(
    135deg,
    rgba(255, 46, 77, 0.05) 0%,
    rgba(124, 58, 237, 0.05) 100%
  );
  border-color: rgba(255, 46, 77, 0.2);
}

.feature-card.primary {
  background: linear-gradient(
    135deg,
    rgba(255, 46, 77, 0.1) 0%,
    rgba(124, 58, 237, 0.1) 100%
  );
  border-color: rgba(255, 46, 77, 0.3);
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  border-color: var(--border-hover);
  box-shadow: var(--shadow-medium);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-title {
  font-size: 24px;
  margin-bottom: 16px;
}

.feature-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--accent-red);
  font-weight: bold;
}

.feature-icon {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 1rem;
}

@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .feature-card {
    padding: 30px;
  }
}

/* CTA Section */
.cta {
  padding: 100px 0;
  text-align: center;
  position: relative;
  background: var(--bg-secondary);
}

.cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  margin-left: -50vw;
  margin-right: -50vw;
  background: var(--bg-secondary);
  z-index: -1;
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: 3.5rem;
  font-weight: 900;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.1;
}

.cta-content > p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 50px;
  line-height: 1.6;
}

.cta-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.email-input {
  flex: 1;
  padding: 18px 24px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.email-input:focus {
  outline: none;
  border-color: var(--accent-red);
  box-shadow: 0 0 0 3px rgba(255, 46, 77, 0.1);
}

.email-input::placeholder {
  color: var(--text-secondary);
}

.btn-large {
  padding: 14px 32px;
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
}

.form-note {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-top: 15px;
}

.success-message {
  text-align: center;
  padding: 40px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  max-width: 400px;
  margin: 0 auto;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.success-message h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.success-message p {
  color: var(--text-secondary);
  margin: 0;
}

@media (max-width: 768px) {
  .cta {
    padding: 80px 0;
  }

  .cta-content h2 {
    font-size: 2.5rem;
  }

  .form-group {
    flex-direction: column;
    gap: 15px;
  }

  .btn-large {
    width: 100%;
  }
}

/* Footer Section */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 80px 0 40px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
}

.footer-brand {
  max-width: 400px;
}

.brand-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-top: 20px;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.link-group h4 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 20px;
  font-size: 1rem;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 12px;
}

.link-group a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

.link-group a:hover {
  color: var(--text-primary);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 40px;
  border-top: 1px solid var(--border-color);
}

.footer-legal {
  display: flex;
  align-items: center;
  gap: 30px;
}

.footer-legal p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.legal-links {
  display: flex;
  gap: 20px;
}

.legal-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: var(--text-primary);
}

.footer-social {
  display: flex;
  gap: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  color: var(--text-primary);
  border-color: var(--accent-red);
  background: var(--accent-red) / 10;
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 30px;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-legal {
    flex-direction: column;
    gap: 15px;
  }

  .legal-links {
    justify-content: center;
  }
}

/* Demo Section */
.demo {
  padding: 120px 0;
  background: var(--bg-primary);
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
  border-color: var(--border-hover);
}

.demo-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.demo-status {
  padding: 4px 12px;
  background: var(--accent-red) / 10;
  color: var(--accent-red);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.demo-content {
  padding: 24px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.photo-item {
  aspect-ratio: 1;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.insight {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.insight-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.insight-value {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.persona-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.persona-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.persona-item.active {
  border-color: var(--accent-red);
  background: var(--accent-red) / 5;
}

.persona-item:hover {
  border-color: var(--border-hover);
}

.persona-avatar {
  width: 48px;
  height: 48px;
  background: var(--bg-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.persona-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.persona-info p {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.generated-post {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.post-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  align-items: center;
}

.post-avatar {
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.post-info h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.post-info span {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.post-content {
  padding: 16px;
}

.post-content p {
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0 0 16px 0;
  font-size: 0.9rem;
}

.post-stats {
  display: flex;
  gap: 20px;
}

.post-stats span {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .demo {
    padding: 80px 0;
  }

  .demo-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .photo-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Feature Icons */
.icon-personas {
  position: relative;
  width: 100%;
  height: 100%;
}

.mini-persona {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
}

.mini-persona.p1 {
  background: var(--accent-red);
  top: 0;
  left: 20px;
}

.mini-persona.p2 {
  background: var(--accent-purple);
  top: 20px;
  left: 0;
}

.mini-persona.p3 {
  background: var(--accent-cyan);
  top: 20px;
  right: 0;
}

.icon-camera {
  position: relative;
  width: 50px;
  height: 40px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 2px solid var(--accent-cyan);
}

.camera-lens {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-cyan);
}

.camera-flash {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 12px;
  height: 12px;
  background: var(--accent-red);
  border-radius: 2px;
}

/* Trend Detection Icon */
.icon-trend {
  position: relative;
  width: 60px;
  height: 60px;
}

.trend-line {
  position: absolute;
  height: 2px;
  background: var(--gradient-primary);
  transform-origin: left center;
}

.trend-line.line-1 {
  width: 30px;
  top: 40%;
  left: 5px;
  transform: rotate(-20deg);
}

.trend-line.line-2 {
  width: 25px;
  top: 50%;
  left: 20px;
  transform: rotate(15deg);
}

.trend-line.line-3 {
  width: 20px;
  top: 45%;
  right: 10px;
  transform: rotate(-10deg);
}

.trend-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-red);
}

.trend-dot.dot-1 {
  top: 28px;
  left: 8px;
}

.trend-dot.dot-2 {
  top: 18px;
  left: 30px;
  background: var(--accent-purple);
}

.trend-dot.dot-3 {
  top: 22px;
  right: 8px;
  background: var(--accent-cyan);
}

/* Knowledge Integration Icon */
.icon-knowledge {
  position: relative;
  width: 60px;
  height: 60px;
}

.knowledge-sources {
  position: relative;
  width: 100%;
  height: 100%;
}

.source-node {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 2px solid;
}

.source-node.doc {
  top: 10px;
  left: 10px;
  background: var(--accent-red);
  border-color: var(--accent-red);
}

.source-node.link {
  top: 10px;
  right: 10px;
  background: var(--accent-purple);
  border-color: var(--accent-purple);
}

.source-node.feed {
  bottom: 10px;
  left: 10px;
  background: var(--accent-cyan);
  border-color: var(--accent-cyan);
}

.knowledge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--bg-primary);
  border: 2px solid var(--text-primary);
}

/* Viral Icon */
.icon-viral {
  position: relative;
  width: 60px;
  height: 60px;
}

.viral-bubble {
  position: absolute;
  border-radius: 50%;
  border: 2px solid var(--accent-red);
}

.viral-bubble.bubble-1 {
  width: 20px;
  height: 20px;
  top: 20px;
  left: 20px;
}

.viral-bubble.bubble-2 {
  width: 15px;
  height: 15px;
  top: 10px;
  right: 15px;
  border-color: var(--accent-purple);
}

.viral-bubble.bubble-3 {
  width: 18px;
  height: 18px;
  bottom: 10px;
  left: 10px;
  border-color: var(--accent-cyan);
}

.viral-pulse {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 10px;
  left: 10px;
  border-radius: 50%;
  background: var(--accent-red);
  opacity: 0.2;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

/* Analytics Icon */
.icon-analytics {
  position: relative;
  width: 60px;
  height: 60px;
}

.chart-bar {
  position: absolute;
  bottom: 10px;
  width: 8px;
  background: var(--gradient-primary);
  border-radius: 4px 4px 0 0;
}

.chart-bar.bar-1 {
  left: 10px;
  height: 15px;
}

.chart-bar.bar-2 {
  left: 20px;
  height: 25px;
  background: var(--accent-purple);
}

.chart-bar.bar-3 {
  left: 30px;
  height: 20px;
  background: var(--accent-cyan);
}

.chart-bar.bar-4 {
  left: 40px;
  height: 30px;
  background: var(--accent-red);
}

.chart-line {
  position: absolute;
  width: 45px;
  height: 2px;
  background: var(--accent-red);
  top: 15px;
  left: 8px;
  transform: rotate(-15deg);
}

.chart-line::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--accent-red);
  border-radius: 50%;
  right: -3px;
  top: -2px;
}

.feature-title {
  font-size: 24px;
  margin-bottom: 16px;
}

.feature-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--accent-red);
  font-weight: bold;
}

/* Screenshots Section */
.screenshots {
  padding: 100px 0;
  position: relative;
}

.screenshots::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  right: 50%;
  bottom: 0;
  margin-left: -50vw;
  margin-right: -50vw;
  background: var(--bg-primary);
  z-index: -1;
}

.screenshots-showcase {
  display: flex;
  flex-direction: column;
  gap: 100px;
}

.screenshot-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin: 80px 0;
}

.screenshot-item.reverse .screenshot-image {
  order: 2;
}

.screenshot-item.reverse .screenshot-info {
  order: 1;
}

.screenshot-image {
  position: relative;
}

.screenshot-info h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.screenshot-info p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.demo-camera-roll {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.4s ease;
  max-width: 600px;
  margin: 0 auto;
}

.demo-camera-roll:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(255, 46, 77, 0.15);
  border-color: rgba(255, 46, 77, 0.3);
}

.camera-header {
  background: var(--bg-tertiary);
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.camera-title-section {
  margin-bottom: 16px;
}

.camera-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.camera-tip {
  font-size: 12px;
  color: var(--text-secondary);
}

.camera-filter-tabs {
  display: flex;
  gap: 12px;
}

.filter-tab {
  padding: 6px 12px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: var(--accent-red);
  color: white;
  border-color: var(--accent-red);
}

.real-photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 24px;
  background: var(--bg-primary);
}

.real-photo-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.real-photo-card:hover {
  background: var(--bg-tertiary);
  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.2);
  transform: translateY(-4px);
  border-color: var(--accent-cyan);
}

.real-photo-card.selected {
  border-color: var(--accent-red);
  box-shadow: 0 8px 24px rgba(255, 46, 77, 0.25);
  transform: scale(1.02) translateY(-2px);
}

.photo-image-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-placeholder.fashion {
  background: linear-gradient(135deg, #ff6b7a 0%, #c44569 100%);
}

.photo-placeholder.lifestyle {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.photo-placeholder::before {
  content: "📷";
  font-size: 24px;
  opacity: 0.7;
}

.photo-placeholder.fashion::before {
  content: "👗";
}

.photo-placeholder.lifestyle::before {
  content: "☕";
}

.delete-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(4px);
}

.real-photo-card:hover .delete-button {
  opacity: 1;
}

.photo-info {
  padding: 16px;
}

.photo-filename {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-description {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.photo-date {
  font-size: 11px;
  color: var(--text-muted);
}

.photo-status {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.photo-status.used {
  background: rgba(255, 46, 77, 0.15);
  color: var(--accent-red);
}

.photo-status.unused {
  background: var(--bg-tertiary);
  color: var(--text-muted);
}

.photo-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.tag {
  font-size: 10px;
  padding: 2px 6px;
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 8px;
  font-weight: 500;
}

.photo-type-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 10px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 12px;
  font-weight: 600;
  backdrop-filter: blur(4px);
}

/* Persona Manager Compact */
.demo-persona-manager-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  margin: 0 auto;
}

.persona-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 24px;
}

.persona-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.persona-card.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.05);
  transform: scale(1.02);
}

.persona-card-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin: 0 auto 12px;
}

.persona-card-avatar.fiona {
  background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
  /* background: var(--accent-red); */
}

.persona-card-avatar.marcus {
  background: var(--accent-purple);
}

.persona-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.persona-card p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
}

.persona-knowledge-preview {
  width: 120px;
  margin: auto;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.knowledge-chip-mini {
  display: block;
  font-size: 12px;
  padding: 2px 6px;
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 8px;
  font-weight: 500;
}

.knowledge-chip-mini.active {
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
}

.persona-detail-summary {
  padding: 24px;
  background: var(--bg-secondary);
}

.knowledge-sources-summary {
  margin-top: 16px;
}

.knowledge-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.knowledge-chip {
  font-size: 11px;
  padding: 4px 8px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 12px;
  font-weight: 500;
  border: 1px solid var(--border-color);
}

.knowledge-chip.active {
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  border-color: var(--accent-red);
}

/* Content Generation Compact */
.demo-content-generation-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  margin: 0 auto;
}

.generation-process {
  padding: 24px;
  background: var(--bg-primary);
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  min-width: 120px;
  transition: all 0.3s ease;
  position: relative;
}

.step-item.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.08);
  box-shadow: 0 4px 16px rgba(255, 46, 77, 0.15);
}

.step-icon {
  font-size: 28px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.step-item.active .step-icon {
  background: rgba(255, 46, 77, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 46, 77, 0.2);
}

.step-item span {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-align: center;
}

.step-item.active span {
  color: var(--text-primary);
}

.step-arrow {
  font-size: 20px;
  color: var(--accent-red);
  font-weight: bold;
}

.generated-content-preview {
  padding: 24px;
  border-top: 1px solid var(--border-color);
}

.content-example {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
}

.content-header {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.persona-badge {
  font-size: 11px;
  padding: 4px 8px;
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
  border-radius: 12px;
  font-weight: 600;
}

.trend-badge {
  font-size: 11px;
  padding: 4px 8px;
  background: var(--bg-tertiary);
  color: var(--color-text-secondary);
  border-radius: 12px;
  font-weight: 600;
}

.content-body p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.content-platforms {
  display: flex;
  gap: 8px;
}

.platform-icon {
  font-size: 16px;
  opacity: 0.7;
}

/* Responsive Screenshots */
@media (max-width: 768px) {
  .screenshots {
    padding: 80px 0;
  }

  .screenshot-item {
    grid-template-columns: 1fr;
    gap: 40px;
    margin: 60px 0;
  }

  .screenshot-item.reverse .screenshot-image {
    order: 1;
  }

  .screenshot-item.reverse .screenshot-info {
    order: 2;
  }

  .screenshot-info h3 {
    font-size: 1.5rem;
  }

  .demo-camera-roll,
  .demo-persona-manager-compact,
  .demo-content-generation-compact {
    max-width: 100%;
  }

  .real-photo-grid {
    grid-template-columns: 1fr;
  }

  .persona-grid {
    grid-template-columns: 1fr;
  }
}

/* Footer */
.footer {
  padding: 60px 0 30px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-logo .logo-icon.small {
  width: 32px;
  height: 32px;
}

.footer-logo .logo-text {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.footer-tagline {
  color: var(--text-muted);
  font-size: 14px;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 32px;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent-red);
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  font-size: 14px;
}

.footer-bottom p {
  margin: 0;
}

/* Footer Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 20px;
  }

  .footer-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .footer-links {
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .footer-bottom {
    padding-top: 20px;
  }
}
