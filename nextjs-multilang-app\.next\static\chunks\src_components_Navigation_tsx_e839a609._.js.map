{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useTranslations } from \"next-intl\";\nimport LanguageSwitcher from \"./LanguageSwitcher\";\n\nexport default function Navigation() {\n  const tCommon = useTranslations(\"Common\");\n  const tNavigation = useTranslations(\"Navigation\");\n  return (\n    <nav className=\"navbar\">\n      <div className=\"container\">\n        <motion.div\n          className=\"nav-logo\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"logo-icon\">\n            <div className=\"persona-layers\">\n              <div className=\"persona-layer layer-1\"></div>\n              <div className=\"persona-layer layer-2\"></div>\n              <div className=\"persona-layer layer-3\"></div>\n            </div>\n          </div>\n          <span className=\"logo-text\">{tCommon(\"brandName\")}</span>\n        </motion.div>\n\n        <motion.div\n          className=\"nav-cta\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n        >\n          <button className=\"btn-primary\">{tNavigation(\"login\")}</button>\n        </motion.div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,cAAc,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACpC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAK,WAAU;sCAAa,QAAQ;;;;;;;;;;;;8BAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC;wBAAO,WAAU;kCAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;AAKvD;GAjCwB;;QACN,yMAAA,CAAA,kBAAe;QACX,yMAAA,CAAA,kBAAe;;;KAFb", "debugId": null}}]}