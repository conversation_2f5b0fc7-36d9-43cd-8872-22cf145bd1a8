{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\nimport { createNavigation } from \"next-intl/navigation\";\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: [\"en\", \"zh\", \"ja\"],\n\n  // Used when no locale matches\n  defaultLocale: \"en\",\n\n  // The `pathnames` object holds pairs of internal and\n  // external paths. Based on the locale, the external\n  // paths are rewritten to the shared, internal ones.\n  pathnames: {\n    // If all locales use the same pathname, a single\n    // external path can be provided for all locales\n    \"/\": \"/\",\n    \"/about\": {\n      en: \"/about\",\n      zh: \"/about\",\n      ja: \"/about\",\n    },\n  },\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,qOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;KAAK;IAE3B,8BAA8B;IAC9B,eAAe;IAEf,qDAAqD;IACrD,oDAAoD;IACpD,oDAAoD;IACpD,WAAW;QACT,iDAAiD;QACjD,gDAAgD;QAChD,KAAK;QACL,UAAU;YACR,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,iQAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/LanguageSwitcher.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useTranslations, useLocale } from \"next-intl\";\nimport { useRouter, usePathname } from \"@/i18n/routing\";\nimport { useState } from \"react\";\n\nexport default function LanguageSwitcher() {\n  const t = useTranslations(\"LanguageSwitcher\");\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const languages = [\n    { code: \"en\", name: t(\"english\"), flag: \"🇺🇸\" },\n    { code: \"zh\", name: t(\"chinese\"), flag: \"🇨🇳\" },\n    { code: \"ja\", name: t(\"japanese\"), flag: \"🇯🇵\" },\n  ];\n\n  const currentLanguage = languages.find((lang) => lang.code === locale);\n\n  const handleLanguageChange = (newLocale: string) => {\n    router.replace(pathname, { locale: newLocale });\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold text-text-secondary hover:text-text-primary hover:bg-bg-secondary border border-border-color hover:border-accent-cyan transition-all duration-300\"\n      >\n        <span className=\"text-lg\">{currentLanguage?.flag}</span>\n        <span>{currentLanguage?.name}</span>\n        <svg\n          className={`w-4 h-4 transition-transform duration-300 ${\n            isOpen ? \"rotate-180\" : \"\"\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M19 9l-7 7-7-7\"\n          />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-52 bg-bg-secondary rounded-xl border border-border-color backdrop-blur-xl z-50 overflow-hidden\">\n          <div className=\"py-2\">\n            {languages.map((language) => (\n              <button\n                key={language.code}\n                onClick={() => handleLanguageChange(language.code)}\n                className={`w-full text-left px-4 py-3 text-sm font-medium hover:bg-bg-tertiary flex items-center space-x-3 transition-all duration-200 ${\n                  locale === language.code\n                    ? \"text-accent-red bg-accent-red/10 border-l-2 border-accent-red\"\n                    : \"text-text-secondary hover:text-text-primary\"\n                }`}\n              >\n                <span className=\"text-lg\">{language.flag}</span>\n                <span className=\"flex-1\">{language.name}</span>\n                {locale === language.code && (\n                  <svg\n                    className=\"w-4 h-4 text-accent-red\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Overlay to close dropdown when clicking outside */}\n      {isOpen && (\n        <div className=\"fixed inset-0 z-40\" onClick={() => setIsOpen(false)} />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY;QAChB;YAAE,MAAM;YAAM,MAAM,EAAE;YAAY,MAAM;QAAO;QAC/C;YAAE,MAAM;YAAM,MAAM,EAAE;YAAY,MAAM;QAAO;QAC/C;YAAE,MAAM;YAAM,MAAM,EAAE;YAAa,MAAM;QAAO;KACjD;IAED,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;IAE/D,MAAM,uBAAuB,CAAC;QAC5B,OAAO,OAAO,CAAC,UAAU;YAAE,QAAQ;QAAU;QAC7C,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAK,WAAU;kCAAW,4BAAA,sCAAA,gBAAiB,IAAI;;;;;;kCAChD,6LAAC;kCAAM,4BAAA,sCAAA,gBAAiB,IAAI;;;;;;kCAC5B,6LAAC;wBACC,WAAW,AAAC,6CAEX,OADC,SAAS,eAAe;wBAE1B,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;;YAKP,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;4BAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;4BACjD,WAAW,AAAC,+HAIX,OAHC,WAAW,SAAS,IAAI,GACpB,kEACA;;8CAGN,6LAAC;oCAAK,WAAU;8CAAW,SAAS,IAAI;;;;;;8CACxC,6LAAC;oCAAK,WAAU;8CAAU,SAAS,IAAI;;;;;;gCACtC,WAAW,SAAS,IAAI,kBACvB,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;2BAnBV,SAAS,IAAI;;;;;;;;;;;;;;;YA8B3B,wBACC,6LAAC;gBAAI,WAAU;gBAAqB,SAAS,IAAM,UAAU;;;;;;;;;;;;AAIrE;GArFwB;;QACZ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;QACT,yHAAA,CAAA,YAAS;QACP,yHAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/personaroll/nextjs-multilang-app/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useTranslations } from \"next-intl\";\nimport LanguageSwitcher from \"./LanguageSwitcher\";\n\nexport default function Navigation() {\n  const tCommon = useTranslations(\"Common\");\n  const tNavigation = useTranslations(\"Navigation\");\n  return (\n    <nav className=\"navbar\">\n      <div className=\"container\">\n        <motion.div\n          className=\"nav-logo\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"logo-icon\">\n            <div className=\"persona-layers\">\n              <div className=\"persona-layer layer-1\"></div>\n              <div className=\"persona-layer layer-2\"></div>\n              <div className=\"persona-layer layer-3\"></div>\n            </div>\n          </div>\n          <span className=\"logo-text\">{tCommon(\"brandName\")}</span>\n        </motion.div>\n\n        <motion.div\n          className=\"nav-cta\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n        >\n          <LanguageSwitcher />\n          <button className=\"btn-primary\">{tNavigation(\"login\")}</button>\n        </motion.div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,cAAc,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IACpC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAK,WAAU;sCAAa,QAAQ;;;;;;;;;;;;8BAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,6LAAC,yIAAA,CAAA,UAAgB;;;;;sCACjB,6LAAC;4BAAO,WAAU;sCAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAKvD;GAlCwB;;QACN,yMAAA,CAAA,kBAAe;QACX,yMAAA,CAAA,kBAAe;;;KAFb", "debugId": null}}]}